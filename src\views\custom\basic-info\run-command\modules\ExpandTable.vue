<template>
  <div
    class="expand-table-container"
    @wheel="handleWheel"
    @mouseenter="handleMouseEnter"
    @mouseleave="handleMouseLeave"
  >
    <VxeTable
      ref="vxeTableRef"
      :isShowTableHeader="false"
      :tableKey="tableKey"
      :columns="columns"
      :tableData="list"
      :loading="loading"
      :isShowSize="false"
      :isShowColumns="false"
      :isShowFull="false"
      :autoHeight="true"
      :rowConfig="{ isHover: false }"
      min-height="120px"
      max-height="240px"
      size="small"
      @refresh="getList"
    ></VxeTable>
    <OptCmdDetail v-if="showForm" ref="formRef" @ok="onOperationComplete" @close="showForm = false" />
  </div>
</template>

<script lang="jsx">
  import VxeTable from '@/components/VxeTable'
  import OptCmdDetail from '../../opt-command/modules/OptCmdDetail.vue'
  import { getRunmdById } from '../services'
  import { getOperateCmdPage } from '../../opt-command/services'

  export default {
    name: 'ExpandTable',
    components: { VxeTable, OptCmdDetail },
    props: ['row'],
    data() {
      return {
        showForm: false,
        tableKey: 1,
        loading: true,
        list: [],
        selectIds: [],
        columns: [
          {
            type: 'seq',
            title: '序号',
            width: 50,
            slots: {
              default: ({ row, rowIndex }) => {
                return rowIndex + 1
              },
            },
          },
          {
            title: '日期',
            field: 'operateDate',
            minWidth: 120,
            showOverflow: 'tooltip',
            slots: {
              default: ({ row }) => {
                if (row.operateDate) {
                  // 处理日期格式，将 ISO 格式转换为 YYYY-MM-DD HH:mm:ss
                  const date = new Date(row.operateDate)
                  if (!isNaN(date.getTime())) {
                    return date.toLocaleString('zh-CN', {
                      year: 'numeric',
                      month: '2-digit',
                      day: '2-digit',
                      hour: '2-digit',
                      minute: '2-digit',
                      second: '2-digit',
                      hour12: false
                    }).replace(/\//g, '-')
                  }
                }
                return '-'
              }
            }
          },
          { title: '工程名称', field: 'projectName', minWidth: 120, showOverflow: 'tooltip' },
          { title: '操作人', field: 'operateName', minWidth: 100, showOverflow: 'tooltip' },
          { title: '监护人', field: 'guardianName', minWidth: 100, showOverflow: 'tooltip' },
          {
            title: '接收状态',
            field: 'recStatusCode',
            minWidth: 100,
            showOverflow: 'tooltip',
            slots: {
              default: ({ row }) => {
                return row.recStatusCode === 1 ? '已接收' : '未接收'
              }
            }
          },
          {
            title: '操作',
            field: 'operate',
            width: 80,
            align: 'left',
            slots: {
              default: ({ row, rowIndex }) => {
                return (
                  <span>
                    <a onClick={() => this.handleDetail(row)}>详情</a>
                  </span>
                )
              },
            },
          },
        ],
      }
    },
    created() {
      this.getList()
    },
    mounted() {
      // 确保表格渲染完成后设置正确的滚动行为
      this.$nextTick(() => {
        this.setupScrollBehavior()
      })
    },
    methods: {
      getList() {
        this.loading = true

        // 同时调用两个接口
        Promise.all([
          getRunmdById({ runCmdId: this.row.runCmdId }),
          getOperateCmdPage({ cmdCode: this.row.cmdCode, pageNum: 1, pageSize: Number.MAX_SAFE_INTEGER })
        ]).then(([runmdResponse, operateResponse]) => {
          let projectOpInfoList = []
          let operateCmdList = []

          // 获取工程操作信息列表
          if (runmdResponse.code === 200 && runmdResponse.data && runmdResponse.data.projectOpInfoList) {
            projectOpInfoList = runmdResponse.data.projectOpInfoList
          }

          // 获取操作命令分页数据并倒序排列
          if (operateResponse.code === 200 && operateResponse.data && operateResponse.data.data) {
            operateCmdList = operateResponse.data.data.reverse() // 倒序排列
          }

          // 将operateCmdId赋值给projectOpInfoList中对应的项
          if (projectOpInfoList.length > 0 && operateCmdList.length > 0) {
            projectOpInfoList.forEach((item, index) => {
              if (operateCmdList[index] && operateCmdList[index].operateCmdId) {
                item.operateCmdId = operateCmdList[index].operateCmdId
              }
            })
          }

          this.list = projectOpInfoList
          this.loading = false
          // 数据加载完成后重新设置滚动行为
          this.$nextTick(() => {
            this.setupScrollBehavior()
          })
        }).catch(() => {
          this.list = []
          this.loading = false
          this.$nextTick(() => {
            this.setupScrollBehavior()
          })
        })
      },

      // 设置滚动行为
      setupScrollBehavior() {
        const tableBodyWrapper = this.$el.querySelector('.vxe-table--body-wrapper')
        if (tableBodyWrapper) {
          // 确保滚动容器有正确的样式
          tableBodyWrapper.style.overflowY = 'auto'
          tableBodyWrapper.style.overflowX = 'hidden'
          tableBodyWrapper.style.maxHeight = '240px'
        }
      },

      // 处理滚轮事件，智能处理滚动冒泡
      handleWheel(event) {
        // 获取表格的滚动容器
        const tableBodyWrapper = this.$el.querySelector('.vxe-table--body-wrapper')
        if (!tableBodyWrapper) {
          // 如果找不到滚动容器，让事件正常冒泡到父级
          return
        }

        const scrollTop = tableBodyWrapper.scrollTop
        const scrollHeight = tableBodyWrapper.scrollHeight
        const clientHeight = tableBodyWrapper.clientHeight

        // 计算是否需要滚动条（考虑1px的误差）
        const needsScrollbar = scrollHeight > clientHeight + 1

        // 如果不需要滚动条，让事件继续冒泡到父级
        if (!needsScrollbar) {
          return
        }

        // 有滚动条的情况下，先阻止事件冒泡
        event.stopPropagation()

        // 计算滚动距离
        const deltaY = event.deltaY
        const newScrollTop = scrollTop + deltaY

        // 检查是否到达边界
        const atTop = scrollTop <= 0
        const atBottom = scrollTop >= scrollHeight - clientHeight

        // 如果在边界且继续向边界方向滚动，则允许事件冒泡（让父级处理）
        if ((atTop && deltaY < 0) || (atBottom && deltaY > 0)) {
          // 创建新的滚轮事件并在父元素上触发
          const parentElement = this.$el.parentElement
          if (parentElement) {
            const newEvent = new WheelEvent('wheel', {
              deltaY: event.deltaY,
              deltaX: event.deltaX,
              deltaZ: event.deltaZ,
              bubbles: true,
              cancelable: true
            })
            parentElement.dispatchEvent(newEvent)
          }
          return
        }

        // 阻止默认滚动行为
        event.preventDefault()

        // 执行自定义滚动
        if (newScrollTop >= 0 && newScrollTop <= scrollHeight - clientHeight) {
          tableBodyWrapper.scrollTop = newScrollTop
        } else if (newScrollTop < 0) {
          tableBodyWrapper.scrollTop = 0
        } else {
          tableBodyWrapper.scrollTop = scrollHeight - clientHeight
        }
      },

      // 鼠标进入时添加焦点样式
      handleMouseEnter() {
        this.$el.classList.add('expand-table-focused')
      },

      // 鼠标离开时移除焦点样式
      handleMouseLeave() {
        this.$el.classList.remove('expand-table-focused')
      },

      handleDetail(record) {
        this.showForm = true
        this.$nextTick(() => this.$refs.formRef.handle(record))
      },
      onOperationComplete() {
        this.isShowModal = false
      },
    },
  }
</script>

<style lang="less" scoped>
  .expand-table-container {
    min-height: 120px;
    max-height: 240px;
    position: relative;
    overflow: hidden;
    border: 1px solid #e8e8e8;
    border-radius: 4px;

    // 焦点状态样式
    &.expand-table-focused {
      border-color: #1890ff;
      box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
    }
  }

  ::v-deep .vxe-table-box-content {
    .sortable-column-demo.vxe-grid {
      padding-bottom: 0px !important;
      margin-bottom: 0px !important;
    }
  }

  // 确保表格主体有正确的滚动行为
  ::v-deep .vxe-table--body-wrapper {
    min-height: 120px !important;
    max-height: 240px !important;
    overflow-y: auto !important;
    overflow-x: hidden !important;
    position: relative !important;

    // 自定义滚动条样式
    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-track {
      background: #f5f5f5;
      border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb {
      background: #d9d9d9;
      border-radius: 3px;

      &:hover {
        background: #bfbfbf;
      }

      &:active {
        background: #999999;
      }
    }
  }

  // 确保表格内容区域正确显示
  ::v-deep .vxe-table--render-default .vxe-table--body {
    position: relative !important;
  }
</style>
